import { Annotation } from "@langchain/langgraph";
import { StateGraph, END } from "@langchain/langgraph";
import { AIMessage } from "@langchain/core/messages";
import { ChatOpenAI } from "@langchain/openai";
import { ToolNode } from "@langchain/langgraph/prebuilt";
import { coreTools } from "./tools/index.js";
import type { BaseMessageLike } from "@langchain/core/messages";
import { isAIMessageChunk } from "@langchain/core/messages";
import { DEFAULT_OPENAI_CONFIGS } from "./config/models.js";

// 1. Define the state
const StateAnnotation = Annotation.Root({
  messages: Annotation<BaseMessageLike[]>({
    reducer: (x, y) => x.concat(y),
  }),
});

export interface AgentConfig {
  model?: string;
  baseUrl?: string;
  apiKey?: string;
  temperature?: number;
  maxTokens?: number;
}

export class HaiAgent {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private compiledWorkflow: any;
  private config: AgentConfig;

  constructor(config: AgentConfig = {}) {
    this.config = config;
    this.compiledWorkflow = this.initializeWorkflow();
  }

  private initializeWorkflow() {
    // 2. Set up the tools - using core tools from packages/core implementation
    const toolNode = new ToolNode(coreTools);

    // 3. Set up the model with custom config
    const modelConfig = {
      ...DEFAULT_OPENAI_CONFIGS,
      modelName: this.config.model || DEFAULT_OPENAI_CONFIGS.modelName,
      apiKey: this.config.apiKey || DEFAULT_OPENAI_CONFIGS.apiKey,
      temperature: this.config.temperature,
      maxTokens: this.config.maxTokens,
      configuration: {
        ...DEFAULT_OPENAI_CONFIGS.configuration,
        baseURL: this.config.baseUrl || DEFAULT_OPENAI_CONFIGS.configuration.baseURL,
      },
    };

    const model = new ChatOpenAI(modelConfig);
    const boundModel = model.bindTools(coreTools);

    // 4. Define the graph
    const routeMessage = (state: typeof StateAnnotation.State) => {
      const { messages } = state;
      const lastMessage = messages[messages.length - 1] as AIMessage;
      // If no tools are called, we can finish (respond to the user)
      if (!lastMessage?.tool_calls?.length) {
        return END;
      }
      // Otherwise if there is, we continue and call the tools
      return "tools";
    };

    const callModel = async (
      state: typeof StateAnnotation.State,
    ) => {
      // For versions of @langchain/core < 0.2.3, you must call `.stream()`
      // and aggregate the message from chunks instead of calling `.invoke()`.
      const { messages } = state;
      const responseMessage = await boundModel.invoke(messages);
      return { messages: [responseMessage] };
    };

    return new StateGraph(StateAnnotation)
      .addNode("agent", callModel)
      .addNode("tools", toolNode)
      .addEdge("__start__", "agent")
      .addConditionalEdges("agent", routeMessage)
      .addEdge("tools", "agent")
      .compile();
  }

  async *streamMessage(message: string): AsyncGenerator<string, void, unknown> {
    const stream = await this.compiledWorkflow.stream(
      { messages: [{ role: "user", content: message }] },
      { streamMode: "messages" },
    );

    for await (const [messageChunk, _metadata] of stream) {
      if (isAIMessageChunk(messageChunk) && messageChunk.tool_call_chunks?.length) {
        // For tool calls, we might want to show some indication but not the raw args
        yield `[Tool: ${messageChunk.tool_call_chunks[0].name || 'unknown'}]`;
      } else if (messageChunk.content) {
        yield messageChunk.content;
      }
    }
  }

  async processMessage(message: string): Promise<string> {
    let fullResponse = '';
    for await (const chunk of this.streamMessage(message)) {
      fullResponse += chunk;
    }
    return fullResponse;
  }
}

